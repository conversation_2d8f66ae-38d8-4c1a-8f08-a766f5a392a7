import React from 'react';

const WindowTopBar: React.FC = () => {
  // Handler for maximize/restore
  const handleMaximize = () => {
    window.electronAPI?.windowControls?.maximize();
  };
  // Handler for close
  const handleClose = () => {
    window.electronAPI?.windowControls?.close();
  };

  return (
    <div
      id="window-top-bar"
      className="h-6 glass-subtle flex items-center justify-end px-2 select-none"
      style={{ WebkitAppRegion: 'drag', userSelect: 'none' }}
    >
      <div className="flex items-center gap-1">
        <button
          className="w-4 h-4 flex items-center justify-center hover:bg-white/10 rounded transition-colors window-control-button"
          style={{ WebkitAppRegion: 'no-drag' }}
          onClick={handleMaximize}
          aria-label="Enlarge"
        >
          <i className="text-gray-300 text-xs fa-solid fa-expand" />
        </button>
        <button
          className="w-4 h-4 flex items-center justify-center hover:bg-red-500/20 rounded transition-colors window-control-button"
          style={{ WebkitAppRegion: 'no-drag' }}
          onClick={handleClose}
          aria-label="Close"
        >
          <i className="text-gray-300 text-xs fa-solid fa-xmark hover:text-white" />
        </button>
      </div>
    </div>
  );
};

export default WindowTopBar; 