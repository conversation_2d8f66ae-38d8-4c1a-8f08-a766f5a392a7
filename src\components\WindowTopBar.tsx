import React from 'react';

const WindowTopBar: React.FC = () => {
  // Check if we're in Electron and if the window is frameless
  const isElectron = typeof window !== 'undefined' && window.electronAPI
  const [isFrameless, setIsFrameless] = React.useState(true)

  React.useEffect(() => {
    // In development, we might not have a frameless window
    // This is a fallback to hide the custom window controls if native frame is present
    if (isElectron) {
      // You can add logic here to detect if the window is actually frameless
      // For now, we'll assume it should be frameless in Electron
      setIsFrameless(true)
    }
  }, [isElectron])

  // For debugging: Always show in Electron, hide in browser
  if (!isElectron) {
    return null
  }
  // Handler for minimize
  const handleMinimize = () => {
    window.electronAPI?.windowControls?.minimize();
  };
  // Handler for maximize/restore
  const handleMaximize = () => {
    window.electronAPI?.windowControls?.maximize();
  };
  // Handler for close
  const handleClose = () => {
    window.electronAPI?.windowControls?.close();
  };

  return (
    <div
      id="window-top-bar"
      className="h-6 glass-subtle flex items-center justify-end px-2 select-none"
      style={{ WebkitAppRegion: 'drag', userSelect: 'none' }}
    >
      <div className="flex items-center gap-1">
        <button
          className="w-4 h-4 flex items-center justify-center hover:bg-white/10 rounded transition-colors window-control-button"
          style={{ WebkitAppRegion: 'no-drag' }}
          onClick={handleMinimize}
          aria-label="Minimize"
        >
          <i className="text-gray-300 text-xs fa-solid fa-minus" />
        </button>
        <button
          className="w-4 h-4 flex items-center justify-center hover:bg-white/10 rounded transition-colors window-control-button"
          style={{ WebkitAppRegion: 'no-drag' }}
          onClick={handleMaximize}
          aria-label="Maximize"
        >
          <i className="text-gray-300 text-xs fa-solid fa-expand" />
        </button>
        <button
          className="w-4 h-4 flex items-center justify-center hover:bg-red-500/20 rounded transition-colors window-control-button"
          style={{ WebkitAppRegion: 'no-drag' }}
          onClick={handleClose}
          aria-label="Close"
        >
          <i className="text-gray-300 text-xs fa-solid fa-xmark hover:text-white" />
        </button>
      </div>
    </div>
  );
};

export default WindowTopBar; 