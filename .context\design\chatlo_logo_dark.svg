<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 1795 531" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1.80361,0.264854,-0.204272,1.39105,-581.337,-1381.08)">
        <rect x="1100.88" y="807.297" width="289.174" height="255.412" style="fill:rgb(209,212,221);fill-opacity:0.2;"/>
    </g>
    <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
        <path d="M643.925,269.969C646.442,254.19 651.808,247.555 668.294,246.204C672.517,245.892 677.543,246.208 681.783,246.538C693.244,247.431 704.706,248.215 716.176,248.982L822.292,257.114C840.657,258.637 859.197,259.414 877.569,260.937C885.963,261.633 895.424,261.296 901.696,267.784C909.639,276 906.764,287.626 903.585,297.242C892.993,328.549 881.168,359.393 869.274,390.201L855.559,425.503C852.784,432.733 844.456,455.844 839.92,460.301C834.405,462.223 788.743,460.005 779.681,459.721C776.541,465.776 766.588,478.156 762.158,483.54C753.669,493.608 742.957,502.853 732.965,511.954C728.172,516.32 712.201,534.497 704.697,532.154C700.818,530.943 693.205,522.124 689.251,519.039C681.926,513.324 674.12,507.615 666.765,501.881C645.953,485.205 631.705,468.631 619.356,445.102C585.538,445.4 593.408,447.51 588.75,416.486L577.69,343.919C575.797,331.504 571.991,318.824 570.981,306.478C568.425,275.257 604.587,282.696 625.361,284.229C626.314,271.218 632.22,270.128 643.925,269.969ZM667.322,252.205C656.794,253.007 654.06,256.214 650.419,265.7C651.846,263.448 654.037,259.21 656.556,259.014C667.555,258.162 682.802,259.672 693.834,260.319C735.07,262.737 776.381,266.838 817.476,269.86L867.515,273.416C871.974,273.777 884.93,273.112 887.876,275.906C889.452,281.6 873.565,330.383 871.097,340.134C864.824,364.92 858.116,391.003 849.74,415.232C847.285,422.333 845.098,430.878 841.189,437.231C840.099,439.002 840.268,438.994 840.36,440.95C840.55,444.04 840.648,446.276 841.246,449.319C846.595,432.06 854.078,415.115 860.532,398.247C869.642,374.438 878.911,350.775 887.191,326.656C891.697,313.53 897.564,300.68 900.517,287.039C904.522,268.37 890.044,267.247 876.236,266.225C858.496,264.912 840.657,264.22 822.983,262.887L771.766,258.536C740.096,256.123 708.248,254.734 676.566,252.283C673.847,252.073 669.98,251.993 667.322,252.205ZM658.871,263.501C656.694,266.302 656.339,267.809 655.587,271.287L796.455,280.939C813.403,282.197 830.802,282.623 847.672,284.313C855.586,285.106 861.254,292.435 859.861,300.215C856.961,319.836 850.965,338.907 845.745,358C845.307,359.604 845.332,360.914 845.849,362.472C847.165,363.507 846.458,363.294 847.957,363.356C848.847,362.253 848.63,362.704 849.086,361.394L862.121,317.475C864.641,308.947 867.346,300.41 869.279,291.731C870.775,285.02 870.327,283.535 865.322,279.224C856.243,277.865 842.095,277.269 832.543,276.515L768.994,271.584C732.995,268.981 694.696,264.806 658.871,263.501ZM636.084,276.237C633.212,277.571 632.505,282.009 631.778,284.893C689.955,288.806 748.258,289.727 806.195,296.91C816.645,298.206 835.203,297.501 836.959,311.145C839.097,327.747 837.801,345.675 840.128,362.29C841.259,358.462 841.754,356.055 842.456,352.105C846.202,335.655 851.113,318.725 854.273,302.086C855.234,297.027 853.1,289.912 847.024,289.621C783.367,286.577 719.892,280.147 656.228,276.965C649.562,276.632 642.766,275.761 636.084,276.237ZM871.919,279.48C873.855,281.818 875.994,284.487 875.314,287.646C872.504,300.704 857.03,360.198 851.519,368.421C848.789,370.453 842.591,367.748 839.519,366.771C839.611,375.083 839.624,393.484 840.612,401.385C841.644,402.237 843.344,402.213 844.88,402.435C847.206,402.559 846.758,402.886 848.441,401.892C849.311,400.486 857.946,369.711 858.878,366.146C863.398,348.854 868.541,331.385 873.418,314.103C876.614,302.779 879.123,291.514 883.043,280.243C878.768,279.733 876.201,279.503 871.919,279.48ZM589.493,288.594C578.123,291.06 575.436,296.109 576.729,307.361C578.24,320.52 582.144,333.487 583.975,346.577C588.274,377.308 593.612,408.128 597.169,438.935C603.389,439.062 610.429,439.363 616.585,439.274C609.763,421.839 603.911,408.994 611.66,390.161C621.603,365.998 651.972,354.801 675.641,365.089C685.989,369.587 693.461,379.423 698.423,389.277C701.734,381.979 705.819,373.502 712.111,368.35C737.093,347.891 776.577,356.149 790.28,385.366C800.462,409.693 795.953,432.474 783.204,454.642C800.783,455.253 818.46,455.99 836.035,456.328L833.975,361.323C833.538,345.597 833.287,329.824 832.051,314.161C831.605,308.51 827.681,305.229 822.27,304.512C772.3,297.894 720.473,295.59 670.29,292.724L623.854,289.935C612.214,289.183 601.167,287.931 589.493,288.594ZM737.856,363.178C699.986,368.429 707.794,399.09 699.424,400.86C697.121,400.004 695.441,397.7 694.705,395.385C684.755,364.08 650.298,357.471 626.391,378.416C608.529,394.064 612.477,419.498 622.3,438.574C633.479,462.028 647.201,476.815 666.294,493.434C680.69,505.457 692.394,512.513 706.243,526.124C714.361,522.872 722.058,513.322 728.618,507.398C750.011,488.08 773.045,466.003 784.994,439.234C795.858,414.893 790.226,377.935 763.212,366.485C756.487,363.634 744.996,361.865 737.856,363.178ZM840.444,407.919L840.444,427.145L842.435,421.238C843.878,417.043 845.411,412.766 846.646,408.515L840.444,407.919Z" style="fill:rgb(27,62,104);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
        <path d="M597.169,438.935C593.612,408.128 588.274,377.308 583.975,346.577C582.144,333.487 578.24,320.52 576.729,307.361C575.436,296.109 578.123,291.06 589.493,288.594C601.167,287.931 612.214,289.183 623.854,289.935L670.29,292.724C720.473,295.59 772.3,297.894 822.27,304.512C827.681,305.229 831.605,308.51 832.051,314.161C833.287,329.824 833.538,345.597 833.975,361.323L836.035,456.328C818.46,455.99 800.783,455.253 783.204,454.642C795.953,432.474 800.462,409.693 790.28,385.366C776.577,356.149 737.093,347.891 712.111,368.35C705.819,373.502 701.734,381.979 698.423,389.277C693.461,379.423 685.989,369.587 675.641,365.089C651.972,354.801 621.603,365.998 611.66,390.161C603.911,408.994 609.763,421.839 616.585,439.274C610.429,439.363 603.389,439.062 597.169,438.935Z" style="fill:url(#_Linear1);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
        <path d="M650.419,265.7C654.06,256.214 656.794,253.007 667.322,252.205C669.98,251.993 673.847,252.073 676.566,252.283C708.248,254.734 740.096,256.123 771.766,258.536L822.983,262.887C840.657,264.22 858.496,264.912 876.236,266.225C890.044,267.247 904.522,268.37 900.517,287.039C897.564,300.68 891.697,313.53 887.191,326.656C878.911,350.775 869.642,374.438 860.532,398.247C854.078,415.115 846.595,432.06 841.246,449.319C840.648,446.276 840.55,444.04 840.36,440.95C840.268,438.994 840.099,439.002 841.189,437.231C845.098,430.878 847.285,422.333 849.74,415.232C858.116,391.003 864.824,364.92 871.097,340.134C873.565,330.383 889.452,281.6 887.876,275.906C884.93,273.112 871.974,273.777 867.515,273.416L817.476,269.86C776.381,266.838 735.07,262.737 693.834,260.319C682.802,259.672 667.555,258.162 656.556,259.014C654.037,259.21 651.846,263.448 650.419,265.7Z" style="fill:url(#_Linear2);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
        <path d="M655.587,271.287C656.339,267.809 656.694,266.302 658.871,263.501C694.696,264.806 732.995,268.981 768.994,271.584L832.543,276.515C842.095,277.269 856.243,277.865 865.322,279.224C870.327,283.535 870.775,285.02 869.279,291.731C867.346,300.41 864.641,308.947 862.121,317.475L849.086,361.394C848.63,362.704 848.847,362.253 847.957,363.356C846.458,363.294 847.165,363.507 845.849,362.472C845.332,360.914 845.307,359.604 845.745,358C850.965,338.907 856.961,319.836 859.861,300.215C861.254,292.435 855.586,285.106 847.672,284.313C830.802,282.623 813.403,282.197 796.455,280.939L655.587,271.287Z" style="fill:rgb(235,242,250);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
        <path d="M631.778,284.893C632.505,282.009 633.212,277.571 636.084,276.237C642.766,275.761 649.562,276.632 656.228,276.965C719.892,280.147 783.367,286.577 847.024,289.621C853.1,289.912 855.234,297.027 854.273,302.086C851.113,318.725 846.202,335.655 842.456,352.105C841.754,356.055 841.259,358.462 840.128,362.29C837.801,345.675 839.097,327.747 836.959,311.145C835.203,297.501 816.645,298.206 806.195,296.91C748.258,289.727 689.955,288.806 631.778,284.893Z" style="fill:rgb(235,242,250);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
        <path d="M839.519,366.771C842.591,367.748 848.789,370.453 851.519,368.421C857.03,360.198 872.504,300.704 875.314,287.646C875.994,284.487 873.855,281.818 871.919,279.48C876.201,279.503 878.768,279.733 883.043,280.243C879.123,291.514 876.614,302.779 873.418,314.103C868.541,331.385 863.398,348.854 858.878,366.146C857.946,369.711 849.311,400.486 848.441,401.892C846.758,402.886 847.206,402.559 844.88,402.435C843.344,402.213 841.644,402.237 840.612,401.385C839.624,393.484 839.611,375.083 839.519,366.771Z" style="fill:rgb(235,242,250);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
        <path d="M840.444,427.145L840.444,407.919L846.646,408.515C845.411,412.766 843.878,417.043 842.435,421.238L840.444,427.145Z" style="fill:rgb(235,242,250);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(0.878788,0,0,0.878788,169.211,13.3427)">
        <circle cx="1428.99" cy="143.078" r="33" style="fill:rgb(27,62,104);fill-opacity:0.81;"/>
    </g>
    <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
        <path d="M666.294,493.434C647.201,476.815 633.479,462.028 622.3,438.574C612.477,419.498 608.529,394.064 626.391,378.416C650.298,357.471 684.755,364.08 694.705,395.385C695.441,397.7 697.121,400.004 699.424,400.86C707.794,399.09 699.986,368.429 737.856,363.178C744.996,361.865 756.487,363.634 763.212,366.485C790.226,377.935 795.858,414.893 784.994,439.234C773.045,466.003 750.011,488.08 728.618,507.398C722.058,513.322 714.361,522.872 706.243,526.124C692.394,512.513 680.69,505.457 666.294,493.434Z" style="fill:rgb(255,131,131);fill-rule:nonzero;"/>
        <clipPath id="_clip3">
            <path d="M666.294,493.434C647.201,476.815 633.479,462.028 622.3,438.574C612.477,419.498 608.529,394.064 626.391,378.416C650.298,357.471 684.755,364.08 694.705,395.385C695.441,397.7 697.121,400.004 699.424,400.86C707.794,399.09 699.986,368.429 737.856,363.178C744.996,361.865 756.487,363.634 763.212,366.485C790.226,377.935 795.858,414.893 784.994,439.234C773.045,466.003 750.011,488.08 728.618,507.398C722.058,513.322 714.361,522.872 706.243,526.124C692.394,512.513 680.69,505.457 666.294,493.434Z" clip-rule="nonzero"/>
        </clipPath>
        <g clip-path="url(#_clip3)">
            <g transform="matrix(0.326108,-0.142901,0.138918,0.317017,562.412,189.354)">
                <path d="M228.16,520.023C226.392,506.188 223.715,492.834 222.234,479.08C221.503,472.466 223.361,465.715 223.947,459.301C226.748,428.663 241.225,397.91 271.932,386.975C283.481,382.862 291.22,380.447 303.639,379.706C318.616,378.969 342.627,383.994 355.005,393.001C380.566,411.599 386.123,433.796 391.696,462.266L398.478,498.688C404.736,498.369 416.858,495.482 422.029,500.014C423.708,501.485 424.458,508.506 424.771,510.95C429.822,550.409 432.481,589.912 435.803,629.508C436.938,642.867 442.894,651.815 441.629,666.194C425.295,669.759 408.919,672.039 392.44,674.8C371.84,678.251 351.126,681.007 330.471,684.141C300.593,687.912 271.457,692.619 241.908,698.32C239.045,698.872 231.335,699.176 228.604,698.249C225.418,688.998 225.837,673.923 224.323,663.757C217.953,620.995 216.482,577.921 212.73,534.925C212.583,533.244 211.74,525.444 212.72,524.536C215.844,521.643 223.976,520.799 228.16,520.023ZM371.484,508.764C352.643,511.127 333.638,512.729 314.821,515.326C312.758,515.611 299.47,517.33 297.98,517.845C271.236,521.427 244.918,523.28 217.938,527.253C219.389,539.21 220.833,550.827 221.879,562.827C223.334,579.529 223.538,597.107 225.942,613.636C226.536,622.909 227.77,631.883 229.166,641.065C231.778,658.249 232.288,675.865 234.418,693.072C237.488,692.739 243.431,692.24 246.013,691.113C296.534,679.854 349.986,676.849 400.817,666.473C412.365,664.115 424.154,663.333 435.78,660.464C421.721,618.513 427.776,571.45 419.79,528.002C418.371,520.279 418.289,511.982 416.675,504.118C401.488,502.856 386.447,506.142 371.484,508.764ZM305.268,386.724C260.583,388.489 234.622,415.57 230.646,458.829C230.043,465.385 228.239,472.229 228.871,478.99C230.204,492.632 233.013,505.437 234.093,519.255C238.396,518.674 245.039,517.569 249.2,517.364C245.135,484.51 237.371,453.938 259.268,424.497C267.189,413.847 279.655,407.498 292.649,405.315C311.141,403.4 325.003,404.757 339.994,416.708C359.712,432.428 368.589,452.897 373.69,476.929C375.325,484.632 377.505,493.292 378.275,501.169C382.029,500.702 386.502,500.027 390.221,499.767C388.207,486.815 385.677,473.097 383.752,460.243C376.752,413.486 354.639,387.622 305.268,386.724ZM300.512,411.205C281.056,412.989 269.124,419.578 259.766,437.331C247.954,459.736 251.61,481.139 254.55,504.94C255.014,508.693 255.567,512.398 255.492,516.214C260.552,515.175 265.696,514.608 270.833,514.093C286.04,512.569 301.142,510.269 316.279,508.176C335.985,505.416 351.908,503.293 371.666,501.644C363.393,468.93 362.154,437.261 329.093,417.654C319.608,412.029 311.38,410.446 300.512,411.205Z" style="fill:rgb(27,62,104);fill-rule:nonzero;"/>
                <clipPath id="_clip4">
                    <path d="M228.16,520.023C226.392,506.188 223.715,492.834 222.234,479.08C221.503,472.466 223.361,465.715 223.947,459.301C226.748,428.663 241.225,397.91 271.932,386.975C283.481,382.862 291.22,380.447 303.639,379.706C318.616,378.969 342.627,383.994 355.005,393.001C380.566,411.599 386.123,433.796 391.696,462.266L398.478,498.688C404.736,498.369 416.858,495.482 422.029,500.014C423.708,501.485 424.458,508.506 424.771,510.95C429.822,550.409 432.481,589.912 435.803,629.508C436.938,642.867 442.894,651.815 441.629,666.194C425.295,669.759 408.919,672.039 392.44,674.8C371.84,678.251 351.126,681.007 330.471,684.141C300.593,687.912 271.457,692.619 241.908,698.32C239.045,698.872 231.335,699.176 228.604,698.249C225.418,688.998 225.837,673.923 224.323,663.757C217.953,620.995 216.482,577.921 212.73,534.925C212.583,533.244 211.74,525.444 212.72,524.536C215.844,521.643 223.976,520.799 228.16,520.023ZM371.484,508.764C352.643,511.127 333.638,512.729 314.821,515.326C312.758,515.611 299.47,517.33 297.98,517.845C271.236,521.427 244.918,523.28 217.938,527.253C219.389,539.21 220.833,550.827 221.879,562.827C223.334,579.529 223.538,597.107 225.942,613.636C226.536,622.909 227.77,631.883 229.166,641.065C231.778,658.249 232.288,675.865 234.418,693.072C237.488,692.739 243.431,692.24 246.013,691.113C296.534,679.854 349.986,676.849 400.817,666.473C412.365,664.115 424.154,663.333 435.78,660.464C421.721,618.513 427.776,571.45 419.79,528.002C418.371,520.279 418.289,511.982 416.675,504.118C401.488,502.856 386.447,506.142 371.484,508.764ZM305.268,386.724C260.583,388.489 234.622,415.57 230.646,458.829C230.043,465.385 228.239,472.229 228.871,478.99C230.204,492.632 233.013,505.437 234.093,519.255C238.396,518.674 245.039,517.569 249.2,517.364C245.135,484.51 237.371,453.938 259.268,424.497C267.189,413.847 279.655,407.498 292.649,405.315C311.141,403.4 325.003,404.757 339.994,416.708C359.712,432.428 368.589,452.897 373.69,476.929C375.325,484.632 377.505,493.292 378.275,501.169C382.029,500.702 386.502,500.027 390.221,499.767C388.207,486.815 385.677,473.097 383.752,460.243C376.752,413.486 354.639,387.622 305.268,386.724ZM300.512,411.205C281.056,412.989 269.124,419.578 259.766,437.331C247.954,459.736 251.61,481.139 254.55,504.94C255.014,508.693 255.567,512.398 255.492,516.214C260.552,515.175 265.696,514.608 270.833,514.093C286.04,512.569 301.142,510.269 316.279,508.176C335.985,505.416 351.908,503.293 371.666,501.644C363.393,468.93 362.154,437.261 329.093,417.654C319.608,412.029 311.38,410.446 300.512,411.205Z" clip-rule="nonzero"/>
                </clipPath>
                <g clip-path="url(#_clip4)">
                    <g transform="matrix(1,3.33067e-16,0,1,-56.1733,357.143)">
                        <path d="M228.16,520.023C226.392,506.188 223.715,492.834 222.234,479.08C221.503,472.466 223.361,465.715 223.947,459.301C226.748,428.663 241.225,397.91 271.932,386.975C283.481,382.862 291.22,380.447 303.639,379.706C318.616,378.969 342.627,383.994 355.005,393.001C380.566,411.599 386.123,433.796 391.696,462.266L398.478,498.688C404.736,498.369 416.858,495.482 422.029,500.014C423.708,501.485 424.458,508.506 424.771,510.95C429.822,550.409 432.481,589.912 435.803,629.508C436.938,642.867 442.894,651.815 441.629,666.194C425.295,669.759 408.919,672.039 392.44,674.8C371.84,678.251 351.126,681.007 330.471,684.141C300.593,687.912 271.457,692.619 241.908,698.32C239.045,698.872 231.335,699.176 228.604,698.249C225.418,688.998 225.837,673.923 224.323,663.757C217.953,620.995 216.482,577.921 212.73,534.925C212.583,533.244 211.74,525.444 212.72,524.536C215.844,521.643 223.976,520.799 228.16,520.023ZM371.484,508.764C352.643,511.127 333.638,512.729 314.821,515.326C312.758,515.611 299.47,517.33 297.98,517.845C271.236,521.427 244.918,523.28 217.938,527.253C219.389,539.21 220.833,550.827 221.879,562.827C223.334,579.529 223.538,597.107 225.942,613.636C226.536,622.909 227.77,631.883 229.166,641.065C231.778,658.249 232.288,675.865 234.418,693.072C237.488,692.739 243.431,692.24 246.013,691.113C296.534,679.854 349.986,676.849 400.817,666.473C412.365,664.115 424.154,663.333 435.78,660.464C421.721,618.513 427.776,571.45 419.79,528.002C418.371,520.279 418.289,511.982 416.675,504.118C401.488,502.856 386.447,506.142 371.484,508.764ZM305.268,386.724C260.583,388.489 234.622,415.57 230.646,458.829C230.043,465.385 228.239,472.229 228.871,478.99C230.204,492.632 233.013,505.437 234.093,519.255C238.396,518.674 245.039,517.569 249.2,517.364C245.135,484.51 237.371,453.938 259.268,424.497C267.189,413.847 279.655,407.498 292.649,405.315C311.141,403.4 325.003,404.757 339.994,416.708C359.712,432.428 368.589,452.897 373.69,476.929C375.325,484.632 377.505,493.292 378.275,501.169C382.029,500.702 386.502,500.027 390.221,499.767C388.207,486.815 385.677,473.097 383.752,460.243C376.752,413.486 354.639,387.622 305.268,386.724ZM300.512,411.205C281.056,412.989 269.124,419.578 259.766,437.331C247.954,459.736 251.61,481.139 254.55,504.94C255.014,508.693 255.567,512.398 255.492,516.214C260.552,515.175 265.696,514.608 270.833,514.093C286.04,512.569 301.142,510.269 316.279,508.176C335.985,505.416 351.908,503.293 371.666,501.644C363.393,468.93 362.154,437.261 329.093,417.654C319.608,412.029 311.38,410.446 300.512,411.205Z" style="fill:rgb(27,62,104);fill-rule:nonzero;"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
    <g transform="matrix(0.412668,-0.228113,0.206083,0.372814,821.606,108.774)">
        <rect x="1100.88" y="807.297" width="289.174" height="255.412" style="fill:rgb(235,235,235);"/>
    </g>
    <g transform="matrix(0.591387,-0.274983,0.273091,0.587319,1160.38,-89.8989)">
        <path d="M234.093,519.255C233.013,505.437 230.204,492.632 228.871,478.99C228.239,472.229 230.043,465.385 230.646,458.829C234.622,415.57 260.583,388.489 305.268,386.724C354.639,387.622 376.752,413.486 383.752,460.243C385.677,473.097 388.207,486.815 390.221,499.767C386.502,500.027 382.029,500.702 378.275,501.169C377.505,493.292 375.325,484.632 373.69,476.929C368.589,452.897 359.712,432.428 339.994,416.708C325.003,404.757 311.141,403.4 292.649,405.315C279.655,407.498 267.189,413.847 259.268,424.497C237.371,453.938 245.135,484.51 249.2,517.364C245.039,517.569 238.396,518.674 234.093,519.255Z" style="fill:rgb(251,251,251);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(0.589677,-0.258397,0.251195,0.57324,1172.2,-89.6504)">
        <path d="M224.725,452.758C226.605,439.957 230.592,427.385 237.02,416.509M371.484,508.764C352.643,511.127 333.638,512.729 314.821,515.326C312.758,515.611 299.47,517.33 297.98,517.845C271.236,521.427 244.918,523.28 217.938,527.253C219.389,539.21 220.833,550.827 221.879,562.827C223.334,579.529 223.538,597.107 225.942,613.636C226.536,622.909 227.77,631.883 229.166,641.065C231.778,658.249 232.288,675.865 234.418,693.072C237.488,692.739 243.431,692.24 246.013,691.113C296.534,679.854 349.986,676.849 400.817,666.473C412.365,664.115 424.154,663.333 435.78,660.464C421.721,618.513 427.776,571.45 419.79,528.002C418.371,520.279 418.289,511.982 416.675,504.118C401.488,502.856 386.447,506.142 371.484,508.764ZM245.556,457.032C247.286,446.001 251.326,435.175 259.268,424.497M300.512,411.205C281.056,412.989 269.124,419.578 259.766,437.331C247.954,459.736 251.61,481.139 254.55,504.94C255.014,508.693 255.567,512.398 255.492,516.214C260.552,515.175 265.696,514.608 270.833,514.093C286.04,512.569 301.142,510.269 316.279,508.176C335.985,505.416 351.908,503.293 371.666,501.644C363.393,468.93 362.154,437.261 329.093,417.654C319.608,412.029 311.38,410.446 300.512,411.205ZM235.534,419.134C243.355,405.899 256.278,392.549 271.932,386.975C283.481,382.862 291.22,380.447 303.639,379.706C318.616,378.969 342.627,383.994 355.005,393.001C380.566,411.599 386.123,433.796 391.696,462.266L398.478,498.688C404.736,498.369 416.858,495.482 422.029,500.014C423.708,501.485 424.458,508.506 424.771,510.95C429.822,550.409 432.481,589.912 435.803,629.508C436.938,642.867 442.894,651.815 441.629,666.194C425.295,669.759 408.919,672.039 392.44,674.8C371.84,678.251 351.126,681.007 330.471,684.141C300.593,687.912 271.457,692.619 241.908,698.32C239.045,698.872 231.335,699.176 228.604,698.249C225.418,688.998 225.837,673.923 224.323,663.757C217.953,620.995 216.482,577.921 212.73,534.925C212.583,533.244 211.74,525.444 212.72,524.536C215.844,521.643 223.976,520.799 228.16,520.023C226.392,506.188 223.715,492.834 222.234,479.08C221.503,472.466 223.361,465.715 223.947,459.301C224.147,457.118 224.405,454.935 224.725,452.758C226.605,439.957 230.428,416.322 230.428,416.322L277.589,442.293L270.901,455.56L255.357,447.603L221.485,431.028L230.428,416.322M259.268,424.497C267.189,413.847 279.655,407.498 292.649,405.315C311.141,403.4 325.003,404.757 339.994,416.708C359.712,432.428 368.589,452.897 373.69,476.929C375.325,484.632 377.505,493.292 378.275,501.169C382.029,500.702 386.502,500.027 390.221,499.767C388.207,486.815 385.677,473.097 383.752,460.243C376.752,413.486 354.639,387.622 305.268,386.724C260.583,388.489 234.622,415.57 230.646,458.829C230.043,465.385 228.239,472.229 228.871,478.99C230.204,492.632 233.013,505.437 234.093,519.255C238.396,518.674 245.039,517.569 249.2,517.364C246.609,496.426 242.516,476.414 245.556,457.032" style="fill:rgb(27,62,104);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(0.480543,-0.210575,0.210575,0.480543,1244.79,-22.6269)">
        <path d="M319.172,583C313.56,579.804 310.237,576.789 308.21,570.307C304.497,558.437 311.533,546.466 323.464,543.365C330.515,541.533 335.502,543.407 341.538,546.805C353.184,557.91 356.029,568.762 344.452,581.229C347.674,588.122 358.384,609.423 357.448,616.056C354.244,622.096 318.818,629.478 317.106,620.211C316.005,614.248 317.244,601.565 317.86,595.028C318.612,590.138 318.977,588.041 319.172,583Z" style="fill:rgb(27,62,104);fill-rule:nonzero;"/>
    </g>
    <g transform="matrix(1,0,0,1,-1669.9,-1407.5)">
        <g transform="matrix(384,0,0,384,2546.83,1765.76)">
        </g>
        <text x="1650.96px" y="1765.77px" style="font-family:'Rockwell-Bold', 'Rockwell', serif;font-weight:700;font-size:384px;fill:rgb(209,212,221);">Chat</text>
    </g>
    <g transform="matrix(1,0,0,1,870.522,89.4601)">
        <g transform="matrix(384,0,0,384,170.438,291.375)">
            <path d="M0.502,-0.32C0.502,-0.309 0.501,-0.3 0.5,-0.291C0.5,-0.283 0.498,-0.274 0.496,-0.266C0.493,-0.258 0.491,-0.25 0.487,-0.242C0.484,-0.234 0.48,-0.225 0.476,-0.215C0.47,-0.203 0.461,-0.189 0.449,-0.175C0.437,-0.16 0.423,-0.145 0.407,-0.13C0.39,-0.115 0.373,-0.1 0.353,-0.086C0.334,-0.072 0.314,-0.059 0.293,-0.048C0.273,-0.037 0.252,-0.028 0.231,-0.022C0.211,-0.015 0.191,-0.012 0.173,-0.012C0.149,-0.012 0.128,-0.016 0.11,-0.023C0.092,-0.031 0.077,-0.042 0.065,-0.054C0.053,-0.067 0.044,-0.081 0.038,-0.098C0.032,-0.114 0.029,-0.131 0.029,-0.147C0.029,-0.163 0.032,-0.181 0.038,-0.202C0.043,-0.223 0.05,-0.243 0.059,-0.264C0.068,-0.285 0.077,-0.305 0.088,-0.324C0.098,-0.344 0.108,-0.359 0.118,-0.372C0.13,-0.386 0.143,-0.4 0.16,-0.414C0.176,-0.428 0.194,-0.44 0.214,-0.45C0.234,-0.461 0.255,-0.47 0.278,-0.476C0.3,-0.483 0.324,-0.486 0.348,-0.486C0.367,-0.486 0.386,-0.481 0.404,-0.472C0.423,-0.463 0.439,-0.451 0.454,-0.436C0.468,-0.421 0.48,-0.403 0.489,-0.383C0.498,-0.363 0.502,-0.342 0.502,-0.32ZM0.397,-0.309C0.397,-0.337 0.39,-0.359 0.375,-0.375C0.36,-0.39 0.338,-0.398 0.309,-0.398C0.3,-0.398 0.29,-0.396 0.279,-0.39C0.269,-0.384 0.259,-0.377 0.249,-0.368C0.239,-0.36 0.23,-0.351 0.221,-0.341C0.213,-0.331 0.206,-0.322 0.2,-0.314C0.194,-0.306 0.187,-0.296 0.18,-0.282C0.173,-0.269 0.166,-0.255 0.16,-0.24C0.154,-0.226 0.149,-0.211 0.145,-0.196C0.14,-0.181 0.138,-0.167 0.138,-0.156C0.138,-0.15 0.139,-0.144 0.141,-0.138C0.143,-0.132 0.146,-0.127 0.15,-0.122C0.154,-0.118 0.158,-0.114 0.163,-0.111C0.168,-0.108 0.174,-0.106 0.18,-0.106C0.189,-0.106 0.201,-0.109 0.215,-0.114C0.23,-0.119 0.245,-0.126 0.261,-0.135C0.277,-0.144 0.293,-0.155 0.309,-0.168C0.325,-0.181 0.34,-0.195 0.353,-0.21C0.366,-0.225 0.377,-0.241 0.385,-0.258C0.393,-0.274 0.397,-0.292 0.397,-0.309Z" style="fill:rgb(255,131,131);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(384,0,0,384,170.438,291.375)">
        </g>
        <g transform="matrix(384,0,0,384,370.5,291.375)">
        </g>
        <text x="0px" y="291.375px" style="font-family:'SegoeScript-Bold', 'Segoe Script', cursive;font-weight:700;font-size:384px;fill:rgb(255,131,131);">l</text>
    </g>
    <g transform="matrix(1,0,0,1,-4,-0.127505)">
        <path d="M1402.74,99.106L1456.13,102.218L1452.49,122.203C1452.49,122.203 1446.37,111.864 1432.27,110.328C1420.01,108.993 1411.08,116.619 1411.08,116.619L1402.74,99.106Z" style="fill:rgb(200,204,212);"/>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-89.6814,-227.584,227.584,-89.6814,757.825,485.214)"><stop offset="0" style="stop-color:rgb(223,224,233);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(194,199,207);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-116.133,-224.145,224.145,-116.133,863.467,433.119)"><stop offset="0" style="stop-color:rgb(85,147,161);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(206,212,220);stop-opacity:1"/></linearGradient>
    </defs>
</svg>
