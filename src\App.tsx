import { useState, useEffect } from 'react'
import { useAppStore } from './store'
import IconBar from './components/IconBar'
import MobileNavBar from './components/MobileNavBar'
import Sidebar from './components/Sidebar'
import ChatArea from './components/ChatArea'
import HistoryPage from './pages/HistoryPage'
import SettingsPage from './pages/SettingsPage'
import PerformanceMonitor from './components/PerformanceMonitor'
import { ArtifactsSidebar } from './components/artifacts/ArtifactsSidebar'
import { ToastProvider, useArtifactToasts } from './components/artifacts/controls/ArtifactToast'
import { HashRouter as Router, Routes, Route } from 'react-router-dom'
import WindowTopBar from './components/WindowTopBar'

// Component to handle model update notifications
function ModelUpdateListener() {
  const toasts = useArtifactToasts()

  useEffect(() => {
    const handleModelUpdate = (event: CustomEvent) => {
      const { message, type, updateTime, summary } = event.detail
      if (type === 'success') {
        toasts.success(message, 8000, updateTime, summary) // Show for 8 seconds with summary
      } else if (type === 'error') {
        toasts.error(message, 5000)
      } else {
        toasts.info(message, 5000)
      }
    }

    window.addEventListener('model-update-complete', handleModelUpdate as EventListener)

    return () => {
      window.removeEventListener('model-update-complete', handleModelUpdate as EventListener)
    }
  }, [toasts])

  return null
}

function App() {
  const { sidebarOpen, setSidebarOpen } = useAppStore()
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false)

  return (
    <Router>
      <ToastProvider>
        <ModelUpdateListener />
        {/* Custom Window Top Bar */}
        <WindowTopBar />
        <div className="h-screen flex flex-col md:flex-row bg-gray-900 text-white font-sans antialiased selection:bg-primary/60 overflow-hidden">

        {/* Mobile Navigation Bar */}
        <MobileNavBar />

        {/* Desktop Layout */}
        <div className="flex flex-1 min-h-0">
          {/* VSCode-style Icon Bar */}
          <IconBar />

          {/* Sidebar */}
          <div className={`
            ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            md:translate-x-0 transition-transform duration-300 ease-in-out
            fixed md:relative z-40 h-full
            ${sidebarOpen ? 'left-0' : 'left-0'}
            md:left-0
          `}>
            <Sidebar />
          </div>

          {/* Overlay for mobile */}
          {sidebarOpen && (
            <div
              className="md:hidden fixed inset-0 bg-black/50 z-30"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* Main content area */}
          <div className="flex-1 flex flex-col min-w-0 h-full">
            <Routes>
              <Route path="/" element={<ChatArea />} />
              <Route path="/history" element={<HistoryPage />} />
              <Route path="/settings" element={<SettingsPage />} />
            </Routes>
          </div>

          {/* Artifacts Sidebar */}
          <ArtifactsSidebar />
        </div>

        {/* Performance Monitor */}
        <PerformanceMonitor
          isVisible={showPerformanceMonitor}
          onToggle={() => setShowPerformanceMonitor(!showPerformanceMonitor)}
        />
        </div>
      </ToastProvider>
    </Router>
  )
}

export default App
