import { app, BrowserWindow, ipc<PERSON>ain, IpcMainInvokeEvent, dialog } from 'electron'
import { autoUpdater } from 'electron-updater'
import path from 'path'
import { isDev } from './utils'
import { DatabaseManager } from './database'
import { FileSystemManager } from './fileSystem'

class App {
  private mainWindow: BrowserWindow | null = null
  private db: DatabaseManager
  private fileSystem: FileSystemManager

  constructor() {
    this.db = new DatabaseManager()
    this.fileSystem = new FileSystemManager(this.db)
  }

  private createWindow(): void {
    // Following Electron best practices from official documentation
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false, // Don't show until ready-to-show event
      frame: false, // Frameless window for custom chrome
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
      titleBarOverlay: false,
      backgroundColor: '#111827', // Match our app's dark theme (gray-900)
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
      },
    })

    // Use ready-to-show event for graceful window display (Electron best practice)
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show()

        // Open DevTools in development after window is shown
        if (isDev) {
          this.mainWindow.webContents.openDevTools()
        }
      }
    })

    if (isDev) {
      this.mainWindow.loadURL('http://localhost:5173')
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
    }

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })
  }

  private validateSender(frame: any): boolean {
    // Validate the sender is from your app
    if (!frame || !frame.url) return false
    return frame.url.startsWith('file://') ||
           frame.url.startsWith('http://localhost:5173')
  }

  private validateInput(value: any, type: string, maxLength?: number): boolean {
    if (value === null || value === undefined) return false
    if (typeof value !== type) return false
    if (type === 'string' && maxLength && value.length > maxLength) return false
    return true
  }

  private setupIPC(): void {
    // Database operations with validation
    ipcMain.handle('db:getConversations', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getConversations()
    })

    ipcMain.handle('db:getConversation', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getConversation(id)
    })

    ipcMain.handle('db:createConversation', (event: IpcMainInvokeEvent, title: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
      console.log('Main: Creating conversation with title:', title)
      const id = this.db.createConversation(title)
      console.log('Main: Created conversation:', id)
      return id
    })

    ipcMain.handle('db:updateConversation', (event: IpcMainInvokeEvent, id: string, title: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
      return this.db.updateConversation(id, title)
    })

    ipcMain.handle('db:deleteConversation', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.deleteConversation(id)
    })

    ipcMain.handle('db:addMessage', (event: IpcMainInvokeEvent, conversationId: string, message: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      if (!message || typeof message !== 'object') throw new Error('Invalid message object')
      return this.db.addMessage(conversationId, message)
    })

    ipcMain.handle('db:togglePinMessage', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      this.db.togglePinMessage(messageId)
    })

    ipcMain.handle('db:getMessages', (event: IpcMainInvokeEvent, conversationId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getMessages(conversationId)
    })

    ipcMain.handle('db:searchConversations', (event: IpcMainInvokeEvent, searchTerm: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(searchTerm, 'string', 200)) throw new Error('Invalid search term')
      return this.db.searchConversationsAndMessages(searchTerm)
    })

    ipcMain.handle('db:getConversationsWithArtifacts', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getConversationsWithArtifacts()
    })

    // Settings with validation
    ipcMain.handle('settings:get', (event: IpcMainInvokeEvent, key: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
      return this.db.getSetting(key)
    })

    ipcMain.handle('settings:set', (event: IpcMainInvokeEvent, key: string, value: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
      this.db.setSetting(key, value)
      console.log('Settings updated:', key, value)
    })

    // File system operations with validation
    ipcMain.handle('files:getChatloFolderPath', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.fileSystem.getChatloFolderPath()
    })

    ipcMain.handle('files:setChatloFolderPath', async (event: IpcMainInvokeEvent, path: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(path, 'string', 500)) throw new Error('Invalid folder path')
      return await this.fileSystem.setChatloFolderPath(path)
    })

    ipcMain.handle('files:getIndexedFiles', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.fileSystem.getIndexedFiles()
    })

    ipcMain.handle('files:searchFiles', (event: IpcMainInvokeEvent, query: string, limit?: number) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(query, 'string', 200)) throw new Error('Invalid search query')
      const searchLimit = limit && limit > 0 && limit <= 50 ? limit : 10 // Max 50 results
      return this.fileSystem.searchFiles(query, searchLimit)
    })

    ipcMain.handle('files:processFileContent', (event: IpcMainInvokeEvent, fileId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
      return this.fileSystem.processFileContent(fileId)
    })

    ipcMain.handle('files:indexFile', async (event: IpcMainInvokeEvent, filePath: string, processContent?: boolean) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
      return await this.fileSystem.indexFile(filePath, processContent || false)
    })

    ipcMain.handle('files:indexAllFiles', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return await this.fileSystem.indexAllFiles()
    })

    ipcMain.handle('files:copyFileToUploads', async (event: IpcMainInvokeEvent, sourcePath: string, filename?: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(sourcePath, 'string', 500)) throw new Error('Invalid source path')
      if (filename && !this.validateInput(filename, 'string', 255)) throw new Error('Invalid filename')
      return await this.fileSystem.copyFileToUploads(sourcePath, filename)
    })

    // File attachment operations
    ipcMain.handle('files:addFileAttachment', (event: IpcMainInvokeEvent, messageId: string, fileId: string, attachmentType: 'attachment' | 'reference') => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
      if (!['attachment', 'reference'].includes(attachmentType)) throw new Error('Invalid attachment type')
      return this.db.addFileAttachment(messageId, fileId, attachmentType)
    })

    ipcMain.handle('files:getFileAttachments', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getFileAttachments(messageId)
    })

    ipcMain.handle('files:getMessageFiles', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getMessageFiles(messageId)
    })

    ipcMain.handle('files:removeFileAttachment', (event: IpcMainInvokeEvent, attachmentId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(attachmentId, 'string', 100)) throw new Error('Invalid attachment ID')
      this.db.removeFileAttachment(attachmentId)
    })

    ipcMain.handle('files:saveContentAsFile', async (event: IpcMainInvokeEvent, content: string, filename: string, subfolder?: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(content, 'string', 1000000)) throw new Error('Content too large')
      if (!this.validateInput(filename, 'string', 255)) throw new Error('Invalid filename')
      if (subfolder && !this.validateInput(subfolder, 'string', 100)) throw new Error('Invalid subfolder')
      return await this.fileSystem.saveContentAsFile(content, filename, subfolder)
    })

    ipcMain.handle('files:deleteFile', async (event: IpcMainInvokeEvent, fileId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
      return await this.fileSystem.deleteFile(fileId)
    })

    ipcMain.handle('files:getFileContent', async (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
      return this.fileSystem.getFileContent(filePath)
    })

    ipcMain.handle('files:fileExists', (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
      return this.fileSystem.fileExists(filePath)
    })

    ipcMain.handle('files:showOpenDialog', async (event: IpcMainInvokeEvent, options: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      return await dialog.showOpenDialog(this.mainWindow!, options)
    })

    ipcMain.handle('files:showSaveDialog', async (event: IpcMainInvokeEvent, options: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      return await dialog.showSaveDialog(this.mainWindow!, options)
    })

    // Artifact IPC handlers
    ipcMain.handle('db:getArtifacts', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getArtifacts(messageId)
    })

    ipcMain.handle('db:addArtifact', (event: IpcMainInvokeEvent, messageId: string, artifact: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      if (!artifact || typeof artifact !== 'object') throw new Error('Invalid artifact object')
      return this.db.addArtifact(messageId, artifact)
    })

    // Database diagnostics
    ipcMain.handle('db:getDatabaseHealth', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getDatabaseHealth()
    })

    ipcMain.handle('db:createBackup', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.createBackup()
    })

    ipcMain.handle('db:updateArtifact', (event: IpcMainInvokeEvent, id: string, updates: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
      if (!updates || typeof updates !== 'object') throw new Error('Invalid updates object')
      return this.db.updateArtifact(id, updates)
    })

    ipcMain.handle('db:removeArtifact', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
      return this.db.removeArtifact(id)
    })

    ipcMain.handle('db:getConversationArtifacts', (event: IpcMainInvokeEvent, conversationId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getConversationArtifacts(conversationId)
    })

    // Auto-updater IPC handlers
    ipcMain.handle('updater:check-for-updates', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      if (isDev) {
        return { available: false, message: 'Updates not available in development mode' }
      }

      try {
        const result = await autoUpdater.checkForUpdates()
        return { available: result ? result.updateInfo.version !== app.getVersion() : false }
      } catch (error: any) {
        console.error('Error checking for updates:', error)
        return { available: false, error: error.message }
      }
    })

    ipcMain.handle('updater:download-and-install', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      if (isDev) {
        return { success: false, message: 'Updates not available in development mode' }
      }

      try {
        await autoUpdater.downloadUpdate()
        autoUpdater.quitAndInstall()
        return { success: true }
      } catch (error: any) {
        console.error('Error downloading/installing update:', error)
        return { success: false, error: error.message }
      }
    })
  }

  private setupAutoUpdater(): void {
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify()
    }

    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:checking-for-update')
      }
    })

    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-available', info)
      }
    })

    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-not-available')
      }
    })

    autoUpdater.on('error', (err) => {
      console.error('Auto-updater error:', err)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:error', err.message)
      }
    })

    autoUpdater.on('download-progress', (progressObj) => {
      console.log(`Download progress: ${progressObj.percent}%`)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:download-progress', progressObj)
      }
    })

    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-downloaded', info)
      }
    })
  }

  public async init(): Promise<void> {
    await app.whenReady()

    // Initialize file system
    try {
      await this.fileSystem.initializeChatloFolder()
      await this.fileSystem.indexAllFiles()
      console.log('File system initialized successfully')
    } catch (error) {
      console.error('Error initializing file system:', error)
    }

    this.setupIPC()
    this.setupAutoUpdater()
    this.createWindow()

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow()
      }
    })

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit()
      }
    })
  }
}

const application = new App()
application.init().catch(console.error)

ipcMain.on('window-minimize', () => {
  if (application.mainWindow) application.mainWindow.minimize();
});
ipcMain.on('window-maximize', () => {
  if (application.mainWindow) {
    if (application.mainWindow.isMaximized()) {
      application.mainWindow.unmaximize();
    } else {
      application.mainWindow.maximize();
    }
  }
});
ipcMain.on('window-close', () => {
  if (application.mainWindow) application.mainWindow.close();
});
