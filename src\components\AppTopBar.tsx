import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { useNetworkStore } from '../stores/networkStore'
import { useNavigate } from 'react-router-dom'

const AppTopBar: React.FC = () => {
  const navigate = useNavigate()
  const { isOnline, isPrivateMode, togglePrivateMode } = useNetworkStore()
  const { settings } = useAppStore()
  const [currentNewsIndex, setCurrentNewsIndex] = useState(0)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showSearch, setShowSearch] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // Sample news items - in real app, this would come from store/API
  const newsItems = [
    {
      id: 1,
      icon: 'fa-bell',
      text: 'New AI model available - DeepSeek V3 Pro',
      type: 'update'
    },
    {
      id: 2,
      icon: 'fa-star',
      text: 'ChatLo v1.1 released with enhanced artifacts',
      type: 'announcement'
    },
    {
      id: 3,
      icon: 'fa-shield-alt',
      text: 'Privacy mode improvements now available',
      type: 'feature'
    }
  ]

  // Auto-rotate news items
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentNewsIndex((prev) => (prev + 1) % newsItems.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [newsItems.length])

  const handlePrivateModeToggle = () => {
    togglePrivateMode()
  }

  const handleUserProfile = () => {
    setShowUserMenu(!showUserMenu)
  }

  const handleSettings = () => {
    navigate('/settings')
    setShowUserMenu(false)
  }

  const navigateNews = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentNewsIndex((prev) => (prev - 1 + newsItems.length) % newsItems.length)
    } else {
      setCurrentNewsIndex((prev) => (prev + 1) % newsItems.length)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // TODO: Implement global search functionality
      console.log('Searching for:', searchQuery)
      setShowSearch(false)
      setSearchQuery('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setShowSearch(false)
      setSearchQuery('')
    }
  }

  const currentNews = newsItems[currentNewsIndex]

  return (
    <div className="h-12 glass border-b border-white/10 flex items-center px-4 relative">
      {/* Logo Section */}
      <div className="flex items-center gap-3">
        <div className="h-8 w-auto">
          <svg viewBox="0 0 1795 531" className="h-full w-auto">
            <g transform="matrix(1.80361,0.264854,-0.204272,1.39105,-581.337,-1381.08)">
              <rect x="1100.88" y="807.297" width="289.174" height="255.412" style={{fill: 'rgb(209,212,221)', fillOpacity: 0.2}} />
            </g>
            <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
              <path d="M643.925,269.969C646.442,254.19 651.808,247.555 668.294,246.204C672.517,245.892 677.543,246.208 681.783,246.538C693.244,247.431 704.706,248.215 716.176,248.982L822.292,257.114C840.657,258.637 859.197,259.414 877.569,260.937C885.963,261.633 895.424,261.296 901.696,267.784C909.639,276 906.764,287.626 903.585,297.242C892.993,328.549 881.168,359.393 869.274,390.201L855.559,425.503C852.784,432.733 844.456,455.844 839.92,460.301C834.405,462.223 788.743,460.005 779.681,459.721C776.541,465.776 766.588,478.156 762.158,483.54C753.669,493.608 742.957,502.853 732.965,511.954C728.172,516.32 712.201,534.497 704.697,532.154C700.818,530.943 693.205,522.124 689.251,519.039C681.926,513.324 674.12,507.615 666.765,501.881C645.953,485.205 631.705,468.631 619.356,445.102C585.538,445.4 593.408,447.51 588.75,416.486L577.69,343.919C575.797,331.504 571.991,318.824 570.981,306.478C568.425,275.257 604.587,282.696 625.361,284.229C626.314,271.218 632.22,270.128 643.925,269.969Z" style={{fill: 'rgb(27,62,104)', fillRule: 'nonzero'}} />
            </g>
            <g transform="matrix(1.80823,0,0,1.80823,155.235,-432.045)">
              <path d="M597.169,438.935C593.612,408.128 588.274,377.308 583.975,346.577C582.144,333.487 578.24,320.52 576.729,307.361C575.436,296.109 578.123,291.06 589.493,288.594C601.167,287.931 612.214,289.183 623.854,289.935L670.29,292.724C720.473,295.59 772.3,297.894 822.27,304.512C827.681,305.229 831.605,308.51 832.051,314.161C833.287,329.824 833.538,345.597 833.975,361.323L836.035,456.328C818.46,455.99 800.783,455.253 783.204,454.642C795.953,432.474 800.462,409.693 790.28,385.366C776.577,356.149 737.093,347.891 712.111,368.35C705.819,373.502 701.734,381.979 698.423,389.277C693.461,379.423 685.989,369.587 675.641,365.089C651.972,354.801 621.603,365.998 611.66,390.161C603.911,408.994 609.763,421.839 616.585,439.274C610.429,439.363 603.389,439.062 597.169,438.935Z" style={{fill: 'url(#_Linear1)', fillRule: 'nonzero'}} />
            </g>
            <defs>
              <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-89.6814,-227.584,227.584,-89.6814,757.825,485.214)">
                <stop offset="0" style={{stopColor: 'rgb(223,224,233)', stopOpacity: 1}} />
                <stop offset="1" style={{stopColor: 'rgb(194,199,207)', stopOpacity: 1}} />
              </linearGradient>
            </defs>
          </svg>
        </div>
        <span className="text-lg font-bold">
          <span className="text-supplement1">Chat</span>
          <span className="text-secondary">Lo</span>
        </span>
      </div>

      {/* News Update Center */}
      <div className="flex-1 flex items-center justify-center ml-12 mr-[calc(256px-48px)]">
        <div className="flex items-center gap-2 glass-subtle rounded-lg px-3 py-1 w-full max-w-md">
          <button 
            className="p-1 hover:bg-white/10 rounded transition-colors"
            onClick={() => navigateNews('prev')}
          >
            <i className="text-gray-300 text-xs fa-solid fa-chevron-left"></i>
          </button>
          <div className="flex items-center gap-2 flex-1">
            <i className={`text-supplement2 text-xs fa-solid ${currentNews.icon}`}></i>
            <span className="text-xs text-supplement1 truncate">{currentNews.text}</span>
          </div>
          <button 
            className="p-1 hover:bg-white/10 rounded transition-colors"
            onClick={() => navigateNews('next')}
          >
            <i className="text-gray-300 text-xs fa-solid fa-chevron-right"></i>
          </button>
        </div>
      </div>

      {/* Right Side Controls */}
      <div className="flex items-center gap-3">
        {/* Search */}
        {showSearch ? (
          <form onSubmit={handleSearch} className="flex items-center">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Search conversations, files..."
              className="bg-gray-800 border border-gray-600 rounded-lg px-3 py-1 text-sm text-supplement1 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent w-64"
              autoFocus
            />
            <button
              type="button"
              onClick={() => setShowSearch(false)}
              className="ml-2 p-1 hover:bg-white/10 rounded transition-colors"
            >
              <i className="text-gray-400 text-xs fa-solid fa-times"></i>
            </button>
          </form>
        ) : (
          <button
            onClick={() => setShowSearch(true)}
            className="p-2 hover:bg-white/10 rounded-lg transition-colors group relative"
          >
            <i className="text-supplement1 text-sm fa-solid fa-search"></i>
            <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 glass text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Search
            </div>
          </button>
        )}
        {/* Private Mode Toggle */}
        <div className="flex items-center gap-2">
          <span className="text-xs text-supplement1">Private</span>
          <button
            className={`relative inline-flex h-4 w-7 items-center rounded-full transition-colors ${
              isPrivateMode ? 'bg-secondary/80 glow-secondary' : 'bg-gray-600'
            }`}
            onClick={handlePrivateModeToggle}
          >
            <span className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
              isPrivateMode ? 'translate-x-3.5' : 'translate-x-0.5'
            }`}></span>
          </button>
        </div>

        {/* Notifications */}
        <div className="relative">
          <button className="p-2 hover:bg-white/10 rounded-lg transition-colors group relative">
            <i className="text-supplement1 text-sm fa-solid fa-bell"></i>
            {/* Notification badge */}
            <span className="absolute -top-1 -right-1 bg-secondary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              3
            </span>
            <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 glass text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Notifications
            </div>
          </button>
        </div>

        {/* Quick Model Switcher */}
        <button
          className="p-2 hover:bg-white/10 rounded-lg transition-colors group relative"
          onClick={() => navigate('/settings')}
        >
          <i className="text-supplement1 text-sm fa-solid fa-robot"></i>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 glass text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            {settings.selectedModel || 'Select Model'}
          </div>
        </button>

        {/* Connection Status */}
        <div className="flex items-center gap-1">
          <i className={`text-xs ${isOnline ? 'fa-solid fa-wifi text-primary' : 'fa-solid fa-wifi-slash text-gray-400'}`}></i>
          <span className="text-xs text-supplement1">{isOnline ? 'Online' : 'Offline'}</span>
        </div>

        {/* User Profile */}
        <div className="relative">
          <button 
            className="p-2 hover:bg-white/10 rounded-lg transition-colors group relative"
            onClick={handleUserProfile}
          >
            <i className="text-supplement1 text-sm fa-solid fa-user"></i>
            <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 glass text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              User Profile
            </div>
          </button>
          
          {/* User Menu Dropdown */}
          {showUserMenu && (
            <div className="absolute right-0 top-12 glass-strong rounded-lg shadow-lg py-2 w-48 z-50">
              <button 
                className="w-full text-left px-4 py-2 text-sm text-supplement1 hover:bg-white/10 transition-colors"
                onClick={handleSettings}
              >
                <i className="fa-solid fa-cog mr-2"></i>
                Settings
              </button>
              <button 
                className="w-full text-left px-4 py-2 text-sm text-supplement1 hover:bg-white/10 transition-colors"
                onClick={() => setShowUserMenu(false)}
              >
                <i className="fa-solid fa-user-circle mr-2"></i>
                Profile
              </button>
              <hr className="border-white/10 my-1" />
              <button 
                className="w-full text-left px-4 py-2 text-sm text-supplement1 hover:bg-white/10 transition-colors"
                onClick={() => setShowUserMenu(false)}
              >
                <i className="fa-solid fa-sign-out-alt mr-2"></i>
                Sign Out
              </button>
            </div>
          )}
        </div>

        {/* Settings Icon */}
        <button 
          className="p-2 hover:bg-white/10 rounded-lg transition-colors group relative"
          onClick={handleSettings}
        >
          <i className="text-supplement1 text-sm fa-solid fa-cog"></i>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 glass text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Settings
          </div>
        </button>
      </div>

      {/* Click outside to close user menu */}
      {showUserMenu && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </div>
  )
}

export default AppTopBar
